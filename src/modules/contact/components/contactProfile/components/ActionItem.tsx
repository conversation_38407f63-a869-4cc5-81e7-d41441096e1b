import React, { useEffect, useState } from 'react'
import * as SharedStyled from '../../../../../styles/styled'
import {
  dayjsFormat,
  generateUUID,
  getEnumValue,
  getKeysFromObjects,
  getValueByKeyAndMatch,
  hasValues,
  isSuccess,
  notify,
} from '../../../../../shared/helpers/util'
import { RoundButton } from '../../../../../shared/components/button/style'
import Pill from './Pill'
import EditIcon from '../../../../../assets/newIcons/edit.svg'
import { Form, Formik } from 'formik'
import AutoComplete from '../../../../../shared/autoComplete/AutoComplete'
import CustomSelect from '../../../../../shared/customSelect/CustomSelect'
import { SharedDateAndTime } from '../../../../../shared/date/SharedDateAndTime'
import Button from '../../../../../shared/components/button/Button'
import Checkbox from '../../../../../shared/checkbox/Checkbox'
import { ActionItemCont } from '../style'
import { I_Action, I_NextAction } from './ToDoNextProfile'
import { getActionMembers, getSalesActionByMemberId, updateActivity } from '../../../../../logic/apis/sales'

import {
  completeContactAction,
  createActionForContactOpp,
  createContactAction,
  getActionsForContactOpp,
  updateActionForContactOpp,
  updateActivity as updateActivityForContactOpp,
} from '../../../../../logic/apis/contact'
import { colors } from '../../../../../styles/theme'
import { set } from 'immer/dist/internal'

interface IActionItem {
  action: I_Action | any
  allActionData:
    | {
        nextAction: I_Action[]
        history: I_Action[]
      }
    | any
  salesPersonDrop: any[]
  todoSchema: any
  contactData: any
  hasOppManagedFullPermission: boolean
  currentMember: any
  actionsForToDoNext: any[]
  autoFillValuesFromChild: any
  setAutoFillValues: React.Dispatch<React.SetStateAction<any>>
  setActionModal: React.Dispatch<React.SetStateAction<boolean>>
  getAllActionsForContactOpp: () => void
  contactOrOppId: string | undefined
  isContact: boolean
  fetchActivity: () => Promise<void>
}

const ActionItem = ({
  action,
  allActionData,
  salesPersonDrop,
  todoSchema,
  contactData,
  hasOppManagedFullPermission,
  currentMember,
  actionsForToDoNext,
  autoFillValuesFromChild,
  setAutoFillValues,
  setActionModal,
  getAllActionsForContactOpp,
  contactOrOppId,
  isContact,
  fetchActivity,
}: IActionItem) => {
  const [isChecked, setIsChecked] = useState(false)
  const [editTodo, setEditTodo] = useState(false)
  const [toDoLoading, setToDoLoading] = useState(false)
  const [isNewAction, setIsNewAction] = useState(false)

  const isNoAction = isContact
    ? !allActionData?.nextAction?.filter((action: any) => action?.contactId).length
    : !allActionData?.nextAction?.filter((action: any) => action?.oppId).length

  const [initTodoData, setInitTodoData] = useState<I_Action>({
    due: dayjsFormat(new Date(), 'YYYY-MM-DDTHH:mm'),
    type: '',
    completedBy: '',
    body: '',
  })

  const onEditCancel = () => {
    setInitTodoData({
      due: dayjsFormat(new Date(), 'YYYY-MM-DDTHH:mm'),
      type: '',
      completedBy: '',
      body: '',
      _id: '',
      assignTo: '',
    })

    // if (selectedAction?._id) {
    setEditTodo(false)
    setIsChecked(false)
    setIsNewAction(false)

    // }
  }

  const onEditTodo = (action: I_Action) => {
    // if (contactData && contactData?.nextAction)
    //   setInitTodoData({
    //     due: dayjsFormat(contactData?.nextAction.due, 'YYYY-MM-DDTHH:mm'),
    //     body: contactData!.nextAction.body,
    //     type: contactData!.nextAction.type,
    //     completedBy: currentMember._id!,
    //     assignTo: salesPersonDrop?.find((person: any) => person._id === contactData?.nextAction?.assignTo)?.name,
    //   })
    // setSelectedAction(action)

    if (action) {
      setInitTodoData({
        _id: action?._id,
        due: dayjsFormat(action?.due, 'YYYY-MM-DDTHH:mm'),
        body: action?.body,
        type: action?.type,
        completedBy: currentMember._id!,
        assignTo: salesPersonDrop?.find((person: any) => person._id === action?.assignTo)?.name,
      })
    }
  }

  const onTodoComplete = async (nextAction: I_Action) => {
    const checkedActionId = action?._id

    const selectedAction = nextAction?._id
      ? allActionData?.nextAction?.find((action: any) => action?._id! === nextAction?._id!)
      : allActionData?.nextAction?.find((action: any) => action?._id === checkedActionId)

    const actionId = actionsForToDoNext?.find((action: { name: string }) => action?.name === nextAction?.body)?._id

    const assignTo =
      salesPersonDrop?.find((person: any) => person.name === nextAction.assignTo)?._id || currentMember?._id

    const isContactAction = !selectedAction?.oppId

    let nextActionData: I_NextAction = {
      _id: actionId ?? generateUUID(),
      body: nextAction.body,
      createdAt: new Date().toISOString(),
      createdBy: currentMember._id,
      due: new Date(nextAction.due).toISOString(),
      type: nextAction.type,
      assignTo: assignTo ?? undefined,
    }

    try {
      setToDoLoading(true)

      // Edit Action

      if (!isChecked && allActionData?.nextAction?.length && !isNewAction) {
        const responseForEdit = await createActionForContactOpp({
          contactOppId: isContactAction ? selectedAction?.contactId! : selectedAction?.oppId!,
          isContact: isContactAction,
          data: {
            body: nextAction.body,
            currDate: new Date(),
            dueDate: new Date(nextAction.due),
            memberId: currentMember._id!,
            assignTo: assignTo ?? undefined,
            type: nextAction.type,
            id: actionId ?? generateUUID(),
          },
        })

        if (isSuccess(responseForEdit)) {
          // setContactData((prev: any) => ({ ...prev, nextAction: nextActionData }))

          getAllActionsForContactOpp()
          if (selectedAction?._id) {
            if (isContact) {
              const res = await updateActivityForContactOpp(
                {
                  id: contactOrOppId!,
                  memberId: currentMember._id!,
                  body: `Edited:  ${selectedAction?.body} to ${nextAction.body} | Due date: ${new Date(
                    selectedAction?.due
                  ).toLocaleDateString('en-US', {
                    month: '2-digit',
                    day: '2-digit',
                    year: 'numeric',
                  })} ${
                    selectedAction?.due
                      ? new Date(selectedAction?.due).toLocaleTimeString('en-US', {
                          hour: '2-digit',
                          minute: 'numeric',
                        })
                      : ''
                  } to ${new Date(nextAction.due).toLocaleDateString('en-US', {
                    month: '2-digit',
                    day: '2-digit',
                    year: 'numeric',
                  })} ${new Date(nextAction.due).toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: 'numeric',
                  })}`,
                  currDate: new Date().toISOString(),
                },
                contactOrOppId!
              )

              if (isSuccess(res)) {
                notify('Action Updated', 'success')
                fetchActivity()
              }
            } else {
              const res = await updateActivity({
                id: contactOrOppId!,
                memberId: currentMember._id!,
                body: `Edited:  ${selectedAction?.body} to ${nextAction.body} | Due date: ${new Date(
                  selectedAction?.due
                ).toLocaleDateString('en-US', {
                  month: '2-digit',
                  day: '2-digit',
                  year: 'numeric',
                })} ${
                  selectedAction?.due
                    ? new Date(selectedAction?.due).toLocaleTimeString('en-US', {
                        hour: '2-digit',
                        minute: 'numeric',
                      })
                    : ''
                } to ${new Date(nextAction.due).toLocaleDateString('en-US', {
                  month: '2-digit',
                  day: '2-digit',
                  year: 'numeric',
                })} ${new Date(nextAction.due).toLocaleTimeString('en-US', {
                  hour: '2-digit',
                  minute: 'numeric',
                })}`,
                currDate: new Date().toISOString(),
              })

              if (isSuccess(res)) {
                notify('Action Updated', 'success')
                fetchActivity()
              }
            }
          }
        } else {
          throw new Error(responseForEdit?.data?.message)
        }
      } else {
        // action api

        // Complete and Create Action

        const isActionInAlreadyInHistory = allActionData?.history?.find(
          (action: any) => action?._id === selectedAction?._id
        )?._id

        let completeResponse

        if (selectedAction?._id) {
          completeResponse = await updateActionForContactOpp({
            data: {
              body: selectedAction?.body,
              currDate: new Date(),
              dueDate: selectedAction?.due,
              memberId: currentMember._id!, //UserId##
              assignTo: selectedAction?.assignTo,
              type: selectedAction?.type,
              id: isActionInAlreadyInHistory ? generateUUID()! : selectedAction?._id,
            },
            contactOppId: isContactAction ? selectedAction?.contactId! : selectedAction?.oppId!,
            isContact: isContactAction,
          })
        }

        const nextActionId = actionsForToDoNext?.find(
          (action: { name: string }) => action?.name === nextAction?.body
        )?._id

        const response = await createActionForContactOpp({
          contactOppId: isNewAction ? contactOrOppId! : isContactAction ? contactOrOppId! : selectedAction?.oppId!,
          isContact: isNewAction ? isContact : isContactAction,
          data: {
            body: nextAction.body,
            currDate: new Date(),
            dueDate: nextAction.due,
            memberId: currentMember._id!,
            type: nextAction.type,
            assignTo: assignTo ?? undefined,
            id: isNewAction ? generateUUID()! : nextActionId ?? generateUUID(),
          },
        })

        getAllActionsForContactOpp()

        if (isSuccess(response) && isContact) {
          // setContactData((prev: any) => ({ ...prev, nextAction: nextActionData }))
          if (selectedAction?._id) {
            const res1 = await updateActivityForContactOpp(
              {
                id: contactOrOppId!,
                memberId: currentMember._id!,
                body: `Completed action ${selectedAction?.body}`,
                currDate: new Date().toISOString(),
              },
              contactOrOppId!
            )
            const res2 = await updateActivityForContactOpp(
              {
                id: contactOrOppId!,
                memberId: currentMember._id!,
                body: `Created new action ${nextAction?.body}`,
                currDate: new Date().toISOString(),
              },
              contactOrOppId!
            )

            if (isSuccess(res1) && isSuccess(res2)) {
              notify('Action Created', 'success')
              fetchActivity()
            }
          } else {
            const res = await updateActivityForContactOpp(
              {
                id: contactOrOppId!,
                memberId: currentMember._id!,
                body: `Created new action ${nextAction?.body}`,
                currDate: new Date().toISOString(),
              },
              contactOrOppId!
            )
            if (isSuccess(res)) {
              notify('Action Created', 'success')
              fetchActivity()
            }
          }
        } else {
          if (selectedAction?._id) {
            const res1 = await updateActivity({
              id: contactOrOppId!,
              memberId: currentMember._id!,
              body: `Completed action ${selectedAction?.body}`,
              currDate: new Date().toISOString(),
            })
            const res2 = await updateActivity({
              id: contactOrOppId!,
              memberId: currentMember._id!,
              body: `Created new action ${nextActionData.body}`,
              currDate: new Date().toISOString(),
            })

            if (isSuccess(res1) && isSuccess(res2)) {
              notify('Action Created', 'success')
              fetchActivity()
            }
          } else {
            const res = await updateActivity({
              id: contactOrOppId!,
              memberId: currentMember._id!,
              body: `Created new action ${nextAction.body}`,
              currDate: new Date().toISOString(),
            })
            if (isSuccess(res)) {
              notify('Action Created', 'success')
              fetchActivity()
            }
          }
        }
      }
      setIsChecked(false)
      setEditTodo(false)
    } catch (err) {
      console.log('ACTIONS ERR', err)
    } finally {
      setToDoLoading(false)
      setIsNewAction(false)
    }
  }

  const renderForm = (isFormNewAction: boolean) => {
    return (
      <div style={{ width: '100%' }}>
        <Formik
          initialValues={
            isFormNewAction
              ? {
                  due: dayjsFormat(new Date(), 'YYYY-MM-DDTHH:mm'),
                  type: '',
                  completedBy: '',
                  body: '',
                  _id: '',
                  assignTo: '',
                }
              : initTodoData
          }
          onSubmit={onTodoComplete}
          validationSchema={todoSchema}
          enableReinitialize={true}
          validateOnChange={true}
          validateOnBlur={false}
        >
          {({ touched, errors, resetForm, values, setFieldValue }) => {
            useEffect(() => {
              if (autoFillValuesFromChild.type !== '' && autoFillValuesFromChild.name !== '') {
                setFieldValue('type', autoFillValuesFromChild.type)
                setFieldValue('body', autoFillValuesFromChild.name)
              }
            }, [autoFillValuesFromChild, contactData])

            useEffect(() => {
              if (values.type) {
                setAutoFillValues((prev: any) => ({
                  ...prev,
                  type: values.type,
                }))
              }
            }, [values.type])

            useEffect(() => {
              if (
                salesPersonDrop?.length &&
                hasOppManagedFullPermission &&
                (isNewAction || (editTodo && !values?.assignTo))
              ) {
                setFieldValue(
                  'assignTo',
                  salesPersonDrop?.find((person: any) => person._id === currentMember?._id)?.name
                )
                return
              }
              if (salesPersonDrop?.length && hasOppManagedFullPermission && isChecked) {
                setFieldValue(
                  'assignTo',
                  salesPersonDrop?.find((person: any) => person._id === currentMember?._id)?.name
                )
              }
            }, [salesPersonDrop?.length, isChecked, isNewAction, editTodo])

            return (
              <Form>
                {/* stepObject */}
                <SharedStyled.TwoInputDiv>
                  <SharedStyled.FlexBox width="100%" gap="12px">
                    <AutoComplete
                      value={values?.body}
                      options={getKeysFromObjects(actionsForToDoNext ?? [], 'name') ?? []}
                      dropdownHeight={'300px'}
                      labelName="Next Action"
                      stateName="body"
                      setFieldValue={setFieldValue}
                      error={touched.body && errors.body ? true : false}
                      onAddClick={(val: string) => {
                        setAutoFillValues((prev: any) => ({ ...prev, name: val }))
                        setActionModal(true)
                      }}
                      showAddOption
                      setValueOnClick={(val: string) => {
                        setFieldValue(`type`, getValueByKeyAndMatch('type', val, `name`, actionsForToDoNext))
                      }}
                    />
                  </SharedStyled.FlexBox>
                </SharedStyled.TwoInputDiv>
                <div
                  style={{
                    display: 'grid',
                    gap: '10px',
                    gridTemplateColumns: hasOppManagedFullPermission ? '1fr 1fr 1fr' : '1fr 2fr',
                    alignItems: 'flex-start',
                  }}
                >
                  <CustomSelect
                    labelName="Select Type"
                    stateName="type"
                    error={touched.type && errors.type ? true : false}
                    value={values.type}
                    dropDownData={['Task', 'Call', 'Email', 'Text']}
                    setValue={() => {}}
                    setFieldValue={setFieldValue}
                    innerHeight="52px"
                    margin="10px 0 0 0"
                  />

                  <SharedStyled.FlexRow margin="2px 0 0 0">
                    <SharedDateAndTime
                      value={values.due}
                      labelName={'Due Date/Time'}
                      stateName="due"
                      setFieldValue={setFieldValue}
                      error={touched.due && errors.due ? true : false}
                    />
                  </SharedStyled.FlexRow>

                  {hasOppManagedFullPermission && (
                    <CustomSelect
                      labelName="Assign To:"
                      stateName="assignTo"
                      error={touched.assignTo && errors.assignTo ? true : false}
                      value={values.assignTo ? values.assignTo : ''}
                      dropDownData={salesPersonDrop?.map((val) => val.name)}
                      setValue={() => {}}
                      setFieldValue={setFieldValue}
                      innerHeight="52px"
                      margin="10px 0 0 0"
                    />
                  )}
                </div>

                <SharedStyled.FlexBox width="100%" gap="12px" margin="24px 0 0 0">
                  {/* bug: toDoLoading is showing for every button, fix it */}

                  <Button type="submit" className="fit" isLoading={toDoLoading}>
                    Save Action
                  </Button>
                  <Button
                    type="button"
                    onClick={() => {
                      isNoAction && isFormNewAction ? setIsNewAction(false) : onEditCancel()
                    }}
                    className="fit outline"
                  >
                    Cancel
                  </Button>
                </SharedStyled.FlexBox>
              </Form>
            )
          }}
        </Formik>
      </div>
    )
  }

  return (
    <>
      {isNoAction ? (
        <ActionItemCont showBorder={isNewAction}>
          {isNewAction ? renderForm(true) : null}
          {isNewAction ? null : (
            <SharedStyled.FlexRow margin="24px 0 0 0" alignItems="flex-start">
              <Checkbox
                onChange={() => {}}
                disabled
                value={true}
                cursor="pointer"
                style={{
                  width: '30px',
                  marginTop: '0px',
                  paddingTop: '0px',
                }}
              />

              <div className="checkbox-item">
                <div>
                  <SharedStyled.FlexCol gap="2px">
                    <SharedStyled.Text fontSize="14px" fontWeight="bold">
                      <>No Action</>&nbsp; - &nbsp;{' '}
                      <span
                        style={{
                          color: colors.darkBlue,
                          cursor: 'pointer',
                          textDecoration: 'underline',
                        }}
                        onClick={() => {
                          setIsNewAction(true)
                          setIsChecked(false)
                          //   setEditTodo(true)
                        }}
                      >
                        Create
                      </span>
                    </SharedStyled.Text>

                    <div>
                      <Pill
                        path={''}
                        numVal={contactData?.PO ? contactData?.num : undefined}
                        text={isContact ? 'Contact' : `${contactData?.PO}-${contactData?.num}`}
                      />
                    </div>
                  </SharedStyled.FlexCol>
                </div>
              </div>
            </SharedStyled.FlexRow>
          )}
        </ActionItemCont>
      ) : null}

      <>
        <ActionItemCont showBorder={isChecked || editTodo}>
          {isChecked || editTodo ? renderForm(false) : null}
          {!editTodo && hasValues(action) ? (
            <div className="todo-container" key={action?._id}>
              <SharedStyled.FlexRow margin="20px 0 0 0" justifyContent="space-between">
                <SharedStyled.FlexRow alignItems="flex-start">
                  <Checkbox
                    style={{
                      marginTop: '0px',
                      paddingTop: '0px',
                    }}
                    title={action?.body}
                    hideTitle
                    onChange={() => {
                      setIsChecked((prev) => !prev)
                      setEditTodo(false)
                      setInitTodoData({
                        due: dayjsFormat(new Date(), 'YYYY-MM-DDTHH:mm'),
                        type: '',
                        completedBy: '',
                        body: '',
                        _id: '',
                        assignTo: '',
                      })
                    }}
                    value={isChecked}
                    cursor="pointer"
                  />

                  <div className="checkbox-item">
                    <div className={isChecked ? 'strike' : ''}>
                      <SharedStyled.FlexCol gap="2px">
                        <SharedStyled.TooltipContainer
                          width="300px"
                          positionLeft="0"
                          style={{
                            maxWidth: '400px',
                          }}
                          positionBottom="0"
                          positionLeftDecs="100px"
                          positionBottomDecs="25px"
                        >
                          <span
                            className="tooltip-content"
                            style={{
                              textDecoration: 'none',
                            }}
                          >
                            {action?.body}
                          </span>
                          <SharedStyled.Text
                            as={'p'}
                            fontSize="14px"
                            fontWeight="bold"
                            style={{
                              maxWidth: '400px',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                            }}
                            className="action"
                          >
                            {action?.body}
                          </SharedStyled.Text>
                        </SharedStyled.TooltipContainer>
                        <div>
                          <SharedStyled.Text fontSize="12px">
                            <>{action?.type} on </>
                          </SharedStyled.Text>
                          {action?.due ? (
                            <SharedStyled.Text fontSize="12px">
                              <>
                                {dayjsFormat(action?.due, 'M/D/YY')} @ {dayjsFormat(action?.due, 'h:mm a')}
                              </>
                            </SharedStyled.Text>
                          ) : (
                            ''
                          )}
                        </div>

                        <div>
                          <SharedStyled.Text color="grey" fontSize="12px">
                            <>{salesPersonDrop?.find((person: any) => person._id === action.assignTo)?.name || '--'}</>
                            &emsp;
                          </SharedStyled.Text>
                          <Pill
                            margin="0 0 0 10px"
                            numVal={action?.oppId ? action?.num : undefined}
                            path={
                              action?.oppId
                                ? `/${getEnumValue(action?.stageGroup!)}/opportunity/${action?.oppId}`
                                : `/contact/profile/${action?.contactId}/false`
                            }
                            text={action?.oppId ? `${action?.PO}-${action?.num}` : 'Contact'}
                          />
                        </div>
                      </SharedStyled.FlexCol>
                    </div>
                  </div>
                </SharedStyled.FlexRow>

                <div>
                  {!isChecked && (
                    <RoundButton
                      className="edit"
                      onClick={(e) => {
                        e.stopPropagation()
                        setEditTodo((prev) => !prev)

                        onEditTodo(action)
                      }}
                    >
                      <img src={EditIcon} alt="edit icon" />
                    </RoundButton>
                  )}
                </div>
              </SharedStyled.FlexRow>
            </div>
          ) : null}
        </ActionItemCont>
      </>
    </>
  )
}

export default ActionItem
